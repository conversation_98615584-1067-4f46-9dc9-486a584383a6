# LSTM 时间序列分析预测  目录
使用LSTM神经网络进行时间序列数据预测分析。  
基于Tensorflow框架、Kerase接口开发网络模型。  
包含数据清洗，数据特征提取，数据建模，数据预测。  
## 想学习更多深度学习项目，可访问如下链接
1.通过自回归(AR,ARIMA)模型进行时间序列预测合集：https://github.com/yangwohenmai/TimeSeriesForecasting  
2.通过深度学习模型进行时间序列预测合集：https://github.com/yangwohenmai/DeepLearningForTSF  
3.基于NLP的文本分析项目合集：https://github.com/yangwohenmai/NLP  


# LSTM 时间序列分析预测 目录  

# 一、LSTM系列
### 1.LSTM单变量（shampoo-sales）  
+ ***1_1.LSTM单变量1***  
```
  1.香皂销售预测(很多饮料瓶图标)  
```  
+ ***1_2.LSTM单变量2***  
```
  1.观测值缩放  
  2.时间序列转换成稳定数据  
  3.时间序列转监督学习数据  
```
+ ***1_3.LSTM单变量3***  
```
  1.LSTM模型开发  
``` 
+ ***1_4.LSTM单变量4***  
```
  1.完整的LSTM案例  
``` 
+ ***1_5.LSTM单变量5***  
```
  1.更健壮的LSTM案例  
``` 
### 2.LSTM多变量（air_pollution）  
+ ***1_1.LSTM多变量1***  
```
  1.数据输出  
  2.预处理  
```  
+ ***1_2.LSTM多变量2***  
```
  1.LSTM数据预处理   
```
+ ***1_3.LSTM多变量3***  
```
  1.定义&训练模型  
  2.数据预处理  
``` 
### 3.Multi-Step LSTM预测（shampoo-sales）  
+ ***1_1.Multi-Step LSTM预测1***  
```
  1.静态模型预测  
```  
+ ***1_2.Multi-Step LSTM预测2***  
```
  1.多步预测的LSTM网络   
```

# 二、LSTM_Fly(airline-passengers) 
``` 
  1.LSTM回归网络(1→1)  
  2.移动窗口型回归(3→1)  
  3.时间步长型回归(3→1)  
  4.批次之间具有记忆的LSTM  
  5.批次之间具有堆叠的LSTM  
```  


# 长短周期记忆网络(LSTM)
## 一、LSTM的特性
### 1.使用编码器-解码器LSTM来回显随机整数序列
```
  01.准备回声序列数据
  02.预测回声序列
  03.编码器 - 解码器模型
  04.代码2回声序列的一个简易版,可观测数据
```
### 2.输入输出对和TimeDistributed
```
  01.一对一LSTM
  02.多对一LSTM(没有TimeDistributed)
  03.多对多LSTM(具有TimeDistributed)
```
### 3.有状态网络的输入输出对预测
```
  01.输入-输出对
  02.重塑数据
  03.有状态的LSTM网络的完整示例
```
## 二、Keras中长短期记忆模型的5步生命周期
```
  01.Keras中长短期记忆模型的5步操作
  02.Keras中长短期记忆模型的5步操作代码分析
```
## 三、LSTM的数据准备
### 1.处理序列预测中的缺失时间步长
```
  01.创建一个序列的演示
  02.对缺失值进行学习
  03.忽略缺失的学习
  04.删除缺失的序列数据
  05.替换缺失的序列数据
```
### 2.归一化标准化长短期内存网络的数据
```
  01.标准化
  02.归一化
```
### 3.使用差异变换消除趋势和季节性
```
  01.差分用于消除季节性
  02.差分用于消除趋势
```
### 4.在编写one-hot编码序列数据
```
  01.one-hotenoder与Keras
  02.one-hotenoder与scikit学习
  03.手动one-hotenoder
```
### 5.重塑Keras中长短期内存网络的输入数据
```
  01.单输入样本的LSTM示例
  02.具有多个输入功能的LSTM示例
  03.如何为长短期记忆网络准备单变量时间序列数据
```
## 四、使用LSTM建模
### 1.堆叠的长短期记忆网络
```
  01.在Keras中实现堆叠LSTM 2D输出
  02.在Keras中实现堆叠LSTM 3D输出
```
### 2.如何调用Keras中的长短期记忆模型
```
  01.保存模型并加载
```
### 3.如何诊断LSTM模型的过度拟合和欠拟合
```
  01.训练周期不足
  02.合格的例子
  03.过度拟合的例子
  04.多次拟合评估
```
## 五、LSTM实例
### 1.空气质量(多变量预测)
```
  01.数据准备
  02.数据画图展示
  03.将数据转换成监督学习数据
  04.一天预测一天
  05.三天预测一天
```
### 2.洗发水销量(单步预测)
```
  01.数据集图示
  02.构造简单的滞后模型
  03.构造监督型数据结构
  04.数据差分法
  05.数据缩放法
  06.LSTM模型实例
  07.LSTM模型性能测评
  08.代入股票数据测试
  09.不做差分的股票数据预测
  10.抽取数据做验证集画损失图
```
### 3.洗发水销量(多步预测)
```
  01.数据集图示
  02.监督学习型数据准备
  03.静态假数据预测效果
  04.训练神经网络预测
```