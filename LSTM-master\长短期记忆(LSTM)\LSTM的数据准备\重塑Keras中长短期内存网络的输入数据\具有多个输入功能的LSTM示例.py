from numpy import array
data = array([
	[0.1, 1.0],
	[0.2, 0.9],
	[0.3, 0.8],
	[0.4, 0.7],
	[0.5, 0.6],
	[0.6, 0.5],
	[0.7, 0.4],
	[0.8, 0.3],
	[0.9, 0.2],
	[1.0, 0.1]])
# 被构造为1个样本，具有10个时间步长和2个特征
"""(1, 10, 2)
[[[0.1 1. ]
  [0.2 0.9]
  [0.3 0.8]
  [0.4 0.7]
  [0.5 0.6]
  [0.6 0.5]
  [0.7 0.4]
  [0.8 0.3]
  [0.9 0.2]
  [1.  0.1]]]"""
data1 = data.reshape(1, 10, 2)
print(data1.shape)
print(data1)
# 被构造为2个样本，具有5个时间步长和2个特征
"""
(2, 5, 2)
[[[0.1 1. ]
  [0.2 0.9]
  [0.3 0.8]
  [0.4 0.7]
  [0.5 0.6]]

 [[0.6 0.5]
  [0.7 0.4]
  [0.8 0.3]
  [0.9 0.2]
  [1.  0.1]]]"""
data2 = data.reshape(2, 5, 2)
print(data2.shape)
print(data2)