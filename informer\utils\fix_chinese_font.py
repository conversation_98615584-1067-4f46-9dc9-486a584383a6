import matplotlib.pyplot as plt
import matplotlib as mpl
import os
import sys
import platform
from matplotlib.font_manager import FontProperties

def setup_chinese_font():
    """
    配置matplotlib中文字体显示
    
    尝试多种方法来确保中文字体能够正确显示
    
    返回:
        font_prop: 可用的FontProperties对象或None
    """
    print("正在配置中文字体显示...")
    
    # 检查操作系统
    system = platform.system()
    print(f"当前操作系统: {system}")
    
    # 配置matplotlib参数
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    
    # 尝试查找系统中的中文字体
    font_paths = []
    
    if system == 'Windows':
        font_paths = [
            'C:/Windows/Fonts/simhei.ttf',    # 黑体
            'C:/Windows/Fonts/simsun.ttc',    # 宋体
            'C:/Windows/Fonts/simkai.ttf',    # 楷体
            'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
            'C:/Windows/Fonts/msyhbd.ttc',    # 微软雅黑 Bold
        ]
    elif system == 'Linux':
        font_paths = [
            '/usr/share/fonts/truetype/wqy/wqy-microhei.ttc',
            '/usr/share/fonts/truetype/arphic/uming.ttc',
            '/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc',
        ]
    elif system == 'Darwin':  # MacOS
        font_paths = [
            '/System/Library/Fonts/PingFang.ttc',
            '/Library/Fonts/Arial Unicode.ttf',
            '/System/Library/Fonts/STHeiti Light.ttc',
        ]
    
    # 尝试所有可能的字体
    font_prop = None
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                print(f"尝试加载字体: {font_path}")
                font_prop = FontProperties(fname=font_path)
                plt.rcParams['font.sans-serif'] = [font_prop.get_name()]
                print(f"成功加载字体: {font_path}")
                return font_prop
            except Exception as e:
                print(f"加载字体失败: {font_path}, 错误: {str(e)}")
    
    # 如果找不到指定字体，尝试系统默认的中文字体
    try:
        all_fonts = [f.name for f in mpl.font_manager.fontManager.ttflist]
        chinese_fonts = [f for f in all_fonts if any(name in f.lower() for name in 
                        ['simhei', 'simsun', 'noto sans cjk', 'microsoft yahei', 'pingfang', 'heiti', 
                         '黑体', '宋体', '微软雅黑', '苹方'])]
        
        print(f"系统中找到的中文字体: {chinese_fonts}")
        
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = chinese_fonts + ['Arial Unicode MS', 'DejaVu Sans']
            print(f"使用系统中的中文字体: {chinese_fonts[0]}")
            return FontProperties(chinese_fonts[0])
    except Exception as e:
        print(f"查找系统字体失败: {str(e)}")
    
    # 如果以上方法都不行，尝试覆盖matplotlib的字体配置目录
    try:
        print("尝试下载并安装SimHei字体...")
        import requests
        import tempfile
        import shutil
        
        # 获取matplotlib的缓存目录
        mpl_data_dir = os.path.dirname(mpl.matplotlib_fname())
        fonts_dir = os.path.join(mpl_data_dir, 'fonts', 'ttf')
        
        print(f"Matplotlib字体目录: {fonts_dir}")
        
        # SimHei字体下载链接 (这只是一个示例，实际使用时需要确保这个链接是有效的)
        font_url = "https://github.com/StellarCN/scp_zh/raw/master/fonts/SimHei.ttf"
        
        # 创建临时文件来下载字体
        with tempfile.NamedTemporaryFile(delete=False, suffix='.ttf') as tmp:
            response = requests.get(font_url, stream=True)
            if response.status_code == 200:
                tmp.write(response.content)
                tmp_path = tmp.name
                
                # 复制到matplotlib字体目录
                target_path = os.path.join(fonts_dir, 'SimHei.ttf')
                shutil.copy(tmp_path, target_path)
                
                # 删除临时文件
                os.unlink(tmp_path)
                
                # 清除matplotlib字体缓存
                import matplotlib.font_manager as fm
                fm._rebuild()
                
                # 设置字体
                plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
                print(f"成功安装字体到: {target_path}")
                
                return FontProperties(fname=target_path)
            else:
                print(f"下载字体失败: HTTP状态码 {response.status_code}")
    except Exception as e:
        print(f"安装字体失败: {str(e)}")
    
    # 最后的尝试：使用通用Sans字体，虽然可能不支持中文
    print("警告: 未找到合适的中文字体，图形中的中文可能无法正确显示")
    plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Arial']
    
    return None

def save_plot_with_chinese(fig, filename, title=None, xlabel=None, ylabel=None, legend_labels=None, dpi=300):
    """
    使用中文保存matplotlib图形
    
    参数:
        fig: matplotlib图形对象
        filename: 保存文件名
        title: 图表标题
        xlabel: x轴标签
        ylabel: y轴标签
        legend_labels: 图例标签列表
        dpi: 分辨率
    """
    # 获取字体
    font_prop = setup_chinese_font()
    
    # 设置标题和标签
    if title:
        fig.suptitle(title, fontproperties=font_prop)
    
    # 获取当前轴
    ax = fig.gca()
    
    if xlabel:
        ax.set_xlabel(xlabel, fontproperties=font_prop)
    
    if ylabel:
        ax.set_ylabel(ylabel, fontproperties=font_prop)
    
    # 设置图例
    if legend_labels:
        handles, _ = ax.get_legend_handles_labels()
        if handles and len(handles) == len(legend_labels):
            ax.legend(handles, legend_labels, prop=font_prop)
    
    # 保存图形
    fig.savefig(filename, dpi=dpi, bbox_inches='tight')
    print(f"图形已保存到: {filename}")

if __name__ == "__main__":
    # 测试脚本，创建一个包含中文的简单图表
    font_prop = setup_chinese_font()
    
    plt.figure(figsize=(10, 6))
    plt.plot([1, 2, 3, 4], [1, 4, 9, 16], 'ro-', label='测试数据')
    
    plt.title('中文标题测试', fontproperties=font_prop)
    plt.xlabel('X轴标签', fontproperties=font_prop)
    plt.ylabel('Y轴标签', fontproperties=font_prop)
    plt.legend(prop=font_prop)
    plt.grid(True)
    
    plt.savefig('chinese_font_test.png', dpi=300)
    plt.show() 