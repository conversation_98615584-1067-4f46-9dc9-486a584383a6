import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
from vmdpy import VMD
from CFOA.utils.cfoa import cfoa
import os
import sys

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'sans-serif'

# 尝试加载自定义字体
font_prop = None
try:
    # Windows系统中文字体路径
    font_paths = [
        'C:/Windows/Fonts/simhei.ttf',    # 黑体
        'C:/Windows/Fonts/simsun.ttc',    # 宋体
        'C:/Windows/Fonts/msyh.ttc',      # 微软雅黑
    ]
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            font_prop = FontProperties(fname=font_path)
            print(f"已加载系统字体: {font_path}")
            break
            
    if font_prop is None:
        print("未找到系统中文字体，使用默认配置")
except Exception as e:
    print(f"加载字体出错: {str(e)}")

# 创建结果目录
if not os.path.exists('results'):
    os.makedirs('results')

# 加载时间序列数据
def load_time_series(file_path):
    """
    加载时间序列数据
    
    参数:
        file_path: 数据文件路径
    
    返回:
        data: 时间序列数据
        dates: 日期列表
    """
    df = pd.read_csv(file_path)
    dates = pd.to_datetime(df['date'])
    data = df['Target'].values
    return data, dates

# 定义VMD参数优化的适应度函数
def vmd_fitness_function(x):
    """
    改进的VMD参数优化适应度函数，带量纲归一化和模态复杂度评估
    """
    global time_series_data
    
    # 将参数取整
    alpha = int(x[0])
    K = max(2, int(x[1]))  # 至少需要2个模态
    
    try:
        # 执行VMD分解
        u, u_hat, omega = VMD(time_series_data, alpha, 0, K, 0, 1, 1e-7)
        
        # 1. 计算重构误差
        reconstructed_signal = np.sum(u, axis=0)
        mse = np.mean((time_series_data - reconstructed_signal) ** 2)
        
        # 归一化MSE - 使用信号方差进行归一化，得到相对误差
        signal_var = np.var(time_series_data)
        norm_mse = mse / signal_var  # 现在是无量纲的相对误差
        
        # 2. 计算频谱分离度惩罚
        center_freqs = np.zeros(K)
        for i in range(K):
            center_freqs[i] = np.sum(omega * np.abs(u_hat[i])) / np.sum(np.abs(u_hat[i]))
        
        center_freqs = np.sort(center_freqs)
        freq_gaps = np.diff(center_freqs)
        
        # 归一化频谱分离惩罚
        nyquist_freq = 0.5  # 假设归一化频率，最大为0.5
        if len(freq_gaps) > 0:
            min_gap = np.min(freq_gaps)
            # 限制min_gap不超过nyquist_freq
            min_gap = min(min_gap, nyquist_freq)
            # 归一化后的频谱分离度指标 - 值域[0,1]之间
            norm_spectral_penalty = 1 - (min_gap / nyquist_freq)
        else:
            norm_spectral_penalty = 0.5  # 只有一个模态时的默认值
        
        # 3. 计算模态复杂度
        mode_complexity = calculate_modes_complexity(u)
        
        # 4. 综合适应度 - 三者都已归一化，量纲一致
        w1, w2, w3 = 0.3, 0.4, 0.3  # 重构误差、频谱分离惩罚和模态复杂度权重
        fitness = w1 * norm_mse + w2 * norm_spectral_penalty + w3 * mode_complexity
        
        print(f"α={alpha}, K={K}, 归一化MSE={norm_mse:.4f}, 频谱分离度={norm_spectral_penalty:.4f}, "
              f"模态复杂度={mode_complexity:.4f}, 适应度={fitness:.4f}")
        
    except Exception as e:
        print(f"VMD执行错误: {e}")
        fitness = 1e10
    
    return fitness

def calculate_modes_complexity(u):
    """
    计算模态的复杂度
    
    参数:
        u: VMD分解得到的模态，形状为(K, N)
    
    返回:
        complexity: 归一化的模态复杂度，值域[0,1]，值越大越不好
    """
    K = u.shape[0]  # 模态数量
    N = u.shape[1]  # 数据长度
    
    # 1. 计算模态的平滑度 (使用一阶差分的方差)
    smoothness = np.zeros(K)
    for i in range(K):
        # 计算一阶差分
        diff = np.diff(u[i, :])
        # 计算差分的方差，并归一化
        smoothness[i] = np.var(diff) / np.var(u[i, :])
    
    # 归一化平滑度指标 (值越小越平滑)
    if np.max(smoothness) > 0:
        norm_smoothness = smoothness / np.max(smoothness)
    else:
        norm_smoothness = np.zeros(K)
    
    # 2. 计算模态之间的相关性
    corr_sum = 0
    count = 0
    for i in range(K):
        for j in range(i+1, K):
            # 计算皮尔逊相关系数的绝对值
            corr = np.abs(np.corrcoef(u[i, :], u[j, :])[0, 1])
            corr_sum += corr
            count += 1
    
    # 平均相关性 (值越小越好)
    if count > 0:
        avg_correlation = corr_sum / count
    else:
        avg_correlation = 0
    
    # 3. 计算模态能量分布的均匀性
    energies = np.zeros(K)
    for i in range(K):
        energies[i] = np.sum(u[i, :] ** 2)
    
    # 归一化能量
    if np.sum(energies) > 0:
        norm_energies = energies / np.sum(energies)
        # 计算熵值来表示能量分布的均匀性
        entropy = -np.sum(norm_energies * np.log2(norm_energies + 1e-10))
        # 归一化熵值到[0,1]区间，熵值越大越均匀
        max_entropy = np.log2(K)
        if max_entropy > 0:
            norm_entropy = entropy / max_entropy
        else:
            norm_entropy = 0
    else:
        norm_entropy = 0
    
    # 4. 综合复杂度评估 (平滑度和相关性越低越好，熵值越高越好)
    # 这里我们需要将平滑度和相关性转换为惩罚项 (值域[0,1]，值越大越不好)
    smoothness_penalty = np.mean(norm_smoothness)
    correlation_penalty = avg_correlation
    entropy_penalty = 1 - norm_entropy  # 转换为惩罚项
    
    # 模态复杂度的综合评估 (值域[0,1]，值越大越不好)
    w_smooth, w_corr, w_entropy = 0.4, 0.4, 0.2  # 各指标权重
    complexity = w_smooth * smoothness_penalty + w_corr * correlation_penalty + w_entropy * entropy_penalty
    
    return complexity

# 使用CFOA优化VMD参数
def optimize_vmd_parameters(data, SearchAgents_no=10, Max_iterations=50):
    """
    使用CFOA优化VMD参数
    
    参数:
        data: 时间序列数据
        SearchAgents_no: 搜索代理数量
        Max_iterations: 最大迭代次数
    
    返回:
        best_alpha: 最优惩罚因子
        best_K: 最优模态数
    """
    global time_series_data
    time_series_data = data
    
    # 定义参数边界
    # alpha的取值范围为[100, 10000]
    # K的取值范围为[2, 15]
    lb = [100, 2]
    ub = [10000, 15]
    dim = 2
    
    # 调用CFOA算法
    print("开始使用CFOA优化VMD参数...")
    best_pos, best_score, curve = cfoa(
        SearchAgents_no=SearchAgents_no,
        Max_EFs=Max_iterations,
        lb=lb,
        ub=ub,
        dim=dim,
        fobj=vmd_fitness_function
    )
    
    # 将最优参数取整
    best_alpha = int(best_pos[0])
    best_K = max(2, int(best_pos[1]))
    
    print(f"优化完成！最优参数: alpha={best_alpha}, K={best_K}, 适应度值={best_score}")
    return best_alpha, best_K, curve

# 使用优化后的参数进行VMD分解
def perform_vmd_decomposition(data, alpha, K):
    """
    使用优化后的参数进行VMD分解
    
    参数:
        data: 时间序列数据
        alpha: 惩罚因子
        K: 模态数
    
    返回:
        u: 分解后的模态
    """
    # 执行VMD分解 - 直接传递参数
    # VMD(signal, alpha, tau, K, DC, init, tol)
    print(f"使用参数 alpha={alpha}, K={K} 进行VMD分解...")
    u, _, omega = VMD(data, alpha, 0, K, 0, 1, 1e-7)
    
    return u, omega

# 绘制分解结果
def plot_vmd_results(data, u, dates, output_dir='results'):
    """
    绘制VMD分解结果
    
    参数:
        data: 原始时间序列数据
        u: 分解后的模态
        dates: 日期列表
        output_dir: 输出目录
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 绘制原始信号
    plt.figure(figsize=(12, 6))
    plt.plot(dates, data)
    plt.title('原始时间序列', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.xlabel('时间', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.ylabel('数值', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/original_signal.png', dpi=300)
    
    # 绘制各个模态
    K = u.shape[0]
    plt.figure(figsize=(12, 6*K))
    for i in range(K):
        plt.subplot(K, 1, i+1)
        plt.plot(dates, u[i, :])
        plt.title(f'模态 {i+1}', fontproperties=font_prop if 'font_prop' in globals() else None)
        plt.grid(True)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/decomposed_modes.png', dpi=300)
    
    # 绘制重构信号
    reconstructed_signal = np.sum(u, axis=0)
    plt.figure(figsize=(12, 6))
    plt.plot(dates, data, label='原始信号')
    plt.plot(dates, reconstructed_signal, label='重构信号')
    plt.title('原始信号与重构信号对比', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.xlabel('时间', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.ylabel('数值', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.grid(True)
    plt.legend(prop=font_prop if 'font_prop' in globals() else None)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/reconstructed_signal.png', dpi=300)
    
    # 计算重构误差
    mse = np.mean((data - reconstructed_signal) ** 2)
    print(f"重构均方误差: {mse}")
    
    # 显示所有图形
    plt.close('all')

# 将VMD分解结果保存为CSV文件
def save_vmd_results_to_csv(u, dates, output_dir='results/csv'):
    """
    将VMD分解结果保存为CSV文件
    
    参数:
        u: 分解后的模态，形状为(K, N)，K是模态数，N是数据长度
        dates: 日期列表
        output_dir: 输出目录
    """
    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 获取模态数量
    K = u.shape[0]
    
    # 创建DataFrame并保存CSV
    modes_df = pd.DataFrame()
    modes_df['date'] = dates
    
    # 添加每个模态作为一列
    for i in range(K):
        modes_df[f'mode{i+1}'] = u[i, :]
    
    # 保存到CSV文件
    csv_path = os.path.join(output_dir, 'vmd_modes.csv')
    modes_df.to_csv(csv_path, index=False)
    print(f"分解模态已保存到CSV文件: {csv_path}")

# 主函数
def main():
    # 加载时间序列数据
    file_path = 'data/pack_mean_total_data.csv'
    data, dates = load_time_series(file_path)
    
    # 进行参数优化
    best_alpha, best_K, curve = optimize_vmd_parameters(data, SearchAgents_no=10, Max_iterations=50)
    
    # 绘制优化曲线
    plt.figure(figsize=(10, 6))
    plt.plot(curve)
    plt.title('CFOA 优化曲线', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.xlabel('迭代次数', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.ylabel('适应度值', fontproperties=font_prop if 'font_prop' in globals() else None)
    plt.grid(True)
    plt.tight_layout()
    plt.savefig('results/optimization_curve.png', dpi=300)
    
    # 使用优化后的参数进行VMD分解
    u, omega = perform_vmd_decomposition(data, best_alpha, best_K)
    
    # 绘制分解结果
    plot_vmd_results(data, u, dates)
    
    # 保存分解后的模态数据
    np.save('results/vmd_modes.npy', u)
    
    # 保存分解结果到CSV文件
    save_vmd_results_to_csv(u, dates, 'results/csv')
    
    print("VMD分解完成，结果已保存到results目录")

if __name__ == '__main__':
    main() 