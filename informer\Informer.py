import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib as mpl
from matplotlib.font_manager import FontProperties
import os
import sys
import random

from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error, mean_absolute_percentage_error

import torch

print(torch.__version__)
import torch.nn as nn
import torch.optim as optim
from torch.optim.lr_scheduler import ReduceLROnPlateau
from torch.utils.data import DataLoader, TensorDataset

# 设置随机种子函数
def set_seed(seed=42):
    """
    设置所有随机数生成器的种子，确保结果可重复
    
    参数:
        seed: 随机种子数值，默认为42
    """
    random.seed(seed)  # Python的random模块
    np.random.seed(seed)  # NumPy
    torch.manual_seed(seed)  # PyTorch (CPU)
    torch.cuda.manual_seed(seed)  # PyTorch (单GPU)
    torch.cuda.manual_seed_all(seed)  # PyTorch (多GPU)
    
    # 额外设置，进一步确保结果可重复性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    print(f"已设置全局随机种子: {seed}")

# 导入TCN模块
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'TCN-master/TCN'))
from tcn import TemporalConvNet

# 导入字体修复模块
try:
    sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
    from fix_chinese_font import setup_chinese_font, save_plot_with_chinese
    # 设置中文字体
    font_prop = setup_chinese_font()
    print("成功导入中文字体修复模块")
except Exception as e:
    print(f"导入中文字体修复模块失败: {str(e)}")
    # 使用默认字体设置
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    font_prop = None

# 检测字体是否可用并打印调试信息
print("可用字体列表:")
available_fonts = [f.name for f in mpl.font_manager.fontManager.ttflist]
chinese_fonts = [f for f in available_fonts if 'sim' in f.lower() or 'microsoft' in f.lower() or 
                'yahei' in f.lower() or '微软' in f.lower() or '宋体' in f.lower() or '黑体' in f.lower()]
print(f"系统中的中文字体: {chinese_fonts}")

plt.style.use("ggplot")
# 自己写的函数文件functionfile.py
# 如果需要调整TSlib-test.ipynb文件的路径位置 注意同时调整导入的路径
from models import Informer
from utils.timefeatures import time_features

# 解决画图中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False


# 创建TCN-Informer混合模型
class TCN_Informer_Model(nn.Module):
    """
    TCN-Informer混合模型：将TCN用于局部时序特征提取，Informer用于长程依赖建模
    """
    def __init__(self, config):
        super(TCN_Informer_Model, self).__init__()
        self.config = config
        
        # TCN参数设置
        num_channels = [config.d_model] * config.tcn_layers
        kernel_size = config.tcn_kernel_size
        
        # 特征投影层 - 将输入特征映射到适合TCN的维度
        self.feature_projection = nn.Conv1d(
            in_channels=config.enc_in, 
            out_channels=config.d_model,
            kernel_size=1
        )
        
        # TCN模块 - 提取局部时序特征
        self.tcn = TemporalConvNet(
            num_inputs=config.d_model,
            num_channels=num_channels,
            kernel_size=kernel_size,
            dropout=config.tcn_dropout
        )
        
        # TCN输出投影 - 将TCN输出映射回原始特征维度
        self.tcn_projection = nn.Conv1d(
            in_channels=num_channels[-1],
            out_channels=config.enc_in,
            kernel_size=1
        )
        
        # Informer模型 - 处理长程依赖
        self.informer = Informer.Model(config)
        
        # 从配置中获取是否使用残差以及残差权重
        self.use_residual = getattr(config, 'use_residual', True)
        initial_weight = getattr(config, 'residual_weight', 0.1)
        self.residual_weight = nn.Parameter(torch.tensor([initial_weight]))
        
        # 使用GroupNorm替换BatchNorm1d，不依赖batch维度，更稳定
        # GroupNorm(num_groups=1, num_channels=n)等效于LayerNorm但适用于CNN输入格式
        self.norm = nn.GroupNorm(num_groups=1, num_channels=config.enc_in)
        # 另一个选项是 self.norm = nn.InstanceNorm1d(config.enc_in, affine=True)
        
    def forward(self, x_enc, x_mark_enc, x_dec, x_mark_dec, enc_self_mask=None):
        # 保存原始输入用于残差连接
        orig_x_enc = x_enc
        
        # 1. 将输入转换为TCN所需的格式 [batch, feature, seq_len]
        x_tcn = x_enc.transpose(1, 2)
        
        # 2. 投影到更高维度特征空间
        x_tcn = self.feature_projection(x_tcn)
        
        # 3. TCN提取局部特征
        tcn_out = self.tcn(x_tcn)
        
        # 4. 投影回原始维度
        tcn_out = self.tcn_projection(tcn_out)
        
        # 5. 转回序列格式 [batch, seq_len, feature]
        tcn_out = tcn_out.transpose(1, 2)
        
        # 6. 残差连接处理 - 根据配置决定是否使用残差连接
        if self.use_residual:
            enhanced_features = tcn_out + self.residual_weight * orig_x_enc
        else:
            # 完全不使用残差连接，只使用TCN的输出
            enhanced_features = tcn_out
        
        # 归一化 - GroupNorm需要输入形状为 [batch, channel, *]
        enhanced_features_transposed = enhanced_features.transpose(1, 2)  # [batch, feature, seq_len]
        normalized_features = self.norm(enhanced_features_transposed)  # GroupNorm在channel维度上归一化
        enhanced_x = normalized_features.transpose(1, 2)  # 转回 [batch, seq_len, feature]
        
        # 7. 将增强特征输入Informer
        output = self.informer(enhanced_x, x_mark_enc, x_dec, x_mark_dec, enc_self_mask)
        
        return output
            
# 1. 修改 tslib_data_loader 的函数签名和内部逻辑
def tslib_data_loader(window, length_size, batch_size, data, data_mark):
    """
    数据加载器函数，用于加载和预处理时间序列数据，以用于训练模型。

    重要说明：此函数现在返回完整的时间特征，包括未来的pred_len部分！
    这是为了确保在训练和推理阶段使用相同格式的时间特征，避免模型在面对非零的未来时间特征时
    出现预测值被"拉回零中心"的现象，特别是在极值预测方面。

    仅仅适用于 多变量预测多变量（可以单独取单变量的输出），或者单变量预测单变量
    也就是y里也会有外生变量？？

    参数:
    - window: 窗口大小，用于截取输入序列的长度。
    - length_size: 目标序列的长度。
    - batch_size: 批量大小，决定每个训练批次包含的数据样本数量。
    - data: 输入时间序列数据。
    - data_mark: 输入时间序列的数据标记，用于辅助模型训练或增加模型的多样性。

    返回值:
    - dataloader: 数据加载器，用于批量加载处理后的训练数据。
    - x_temp: 处理后的输入数据。
    - y_temp: 处理后的目标数据。
    - x_temp_mark: 处理后的输入数据的标记。
    - y_temp_mark: 处理后的目标数据的标记（只包含label_len部分，不包含未来的pred_len部分）。
    - start_idx: 第一个预测点在原始数据中的索引
    """
    # 使用传入的参数，而不是在内部创建Config
    seq_len = window
    label_len = seq_len // 2 # 修改为与Config类中的比例一致，seq_len=20时，label_len=10
    pred_len = length_size
    
    sequence_length = seq_len + pred_len
    result = np.array([data[i: i + sequence_length] for i in range(len(data) - sequence_length + 1)])
    result_mark = np.array([data_mark[i: i + sequence_length] for i in range(len(data) - sequence_length + 1)])

    # 划分x与y
    x_temp = result[:, :seq_len]
    
    # 修改y_temp的划分方式，包含label_len+pred_len
    y_temp = result[:, seq_len - label_len:seq_len + pred_len]

    x_temp_mark = result_mark[:, :seq_len]
    
    # 修改y_temp_mark的划分方式，包含完整的label_len+pred_len部分
    # 这样在训练时能够与推理时保持一致，使用相同的未来时间特征
    y_temp_mark = result_mark[:, seq_len - label_len:seq_len + pred_len]

    # 第一个预测点的索引
    start_idx = seq_len

    # 转换为Tensor和数据类型
    x_temp = torch.tensor(x_temp).type(torch.float32)
    x_temp_mark = torch.tensor(x_temp_mark).type(torch.float32)
    y_temp = torch.tensor(y_temp).type(torch.float32)
    y_temp_mark = torch.tensor(y_temp_mark).type(torch.float32)

    ds = TensorDataset(x_temp, y_temp, x_temp_mark, y_temp_mark)
    
    # 添加随机种子和工作进程控制，确保可重现性
    g = torch.Generator()
    g.manual_seed(42)  # 固定种子
    dataloader = DataLoader(
        ds, 
        batch_size=batch_size, 
        shuffle=True,
        worker_init_fn=lambda worker_id: np.random.seed(42 + worker_id),  # 为每个工作进程设置种子
        generator=g,
        num_workers=0  # 使用单线程加载数据，避免多线程带来的不确定性
    )

    return dataloader, x_temp, y_temp, x_temp_mark, y_temp_mark, start_idx


# 修复模型训练函数中目标张量形状不匹配的问题
def model_train(net, train_loader, length_size, optimizer, criterion, num_epochs, device, print_train=False):
    """
    训练模型并应用早停机制。

    参数:
        net (torch.nn.Module): 待训练的模型。
        train_loader (torch.utils.data.DataLoader): 训练数据加载器。
        length_size (int): 输出序列的长度。
        optimizer (torch.optim.Optimizer): 优化器。
        criterion (torch.nn.Module): 损失函数。
        num_epochs (int): 总训练轮数。
        device (torch.device): 设备（CPU或GPU）。
        print_train (bool, optional): 是否在训练中打印进度，默认为False。
    返回:
        net (torch.nn.Module): 训练好的模型。
        train_loss (list): 训练过程中每个epoch的平均训练损失列表。
        best_epoch (int): 达到最佳验证损失的epoch数。
    """

    train_loss = []  # 用于记录每个epoch的平均训练损失
    print_frequency = num_epochs / 20  # 计算打印训练状态的频率

    for epoch in range(num_epochs):
        total_train_loss = 0  # 初始化一个epoch的总损失

        net.train()  # 将模型设置为训练模式
        for i, (datapoints, labels, datapoints_mark, labels_mark) in enumerate(train_loader):
            datapoints, labels, datapoints_mark, labels_mark = datapoints.to(device), labels.to(
                device), datapoints_mark.to(device), labels_mark.to(device)
            optimizer.zero_grad()  # 清空梯度
            
            # 修复数据泄露问题：创建正确的解码器输入
            # 获取批次大小、标签长度和特征维度
            batch_size, seq_len, feature_dim = labels.shape
            label_len = seq_len - length_size  # 计算label_len (前面部分)
            
            # 创建解码器输入 - 使用label_len部分作为起始令牌，并提供第一个预测步的合理起点
            dec_inp = torch.zeros((batch_size, seq_len, feature_dim)).to(device)
            dec_inp[:, :label_len, :] = labels[:, :label_len, :]  # 复制label_len部分作为历史数据
            
            # 对于第一个预测位置，使用最后一个历史点作为起始值，避免全零输入导致的"拉向零中心"效应
            dec_inp[:, label_len:label_len+1, :] = labels[:, label_len-1:label_len, :]
            
            # 创建完整的解码器时间特征输入 - 直接使用实际的未来时间特征
            # 注意：labels_mark 现在包含了未来的时间特征，已在 tslib_data_loader 中修改
            full_labels_mark = labels_mark.clone().to(device)
            
            # 获取模型预测，确保输出与目标对齐
            preds = net(datapoints, datapoints_mark, dec_inp, full_labels_mark, None)
            
            # 从标签中提取目标部分，确保形状与预测一致
            # Informer的输出是 [batch, pred_len, feature_dim]
            # 我们需要确保标签也是这个形状
            true_labels = labels[:, -length_size:, :]
            
            # 如果需要，去掉最后一个维度以匹配预测
            if preds.shape[-1] == 1 and true_labels.shape[-1] == 1:
                preds = preds.squeeze(-1)
                true_labels = true_labels.squeeze(-1)
            
            loss = criterion(preds, true_labels)  # 计算损失
            loss.backward()  # 反向传播
            optimizer.step()  # 更新模型参数
            total_train_loss += loss.item()  # 累加损失值

        avg_train_loss = total_train_loss / len(train_loader)  # 计算该epoch的平均损失
        train_loss.append(avg_train_loss)  # 将平均损失添加到列表中

        # 如果设置为打印训练状态，则按频率打印
        if print_train:
            if (epoch + 1) % print_frequency == 0:
                print(f"Epoch: {epoch + 1}, Train Loss: {avg_train_loss:.4f}")

    return net, train_loss, epoch + 1


def model_train_val(net, train_loader, val_loader, length_size, optimizer, criterion, scheduler, num_epochs, device,
                    early_patience=0.15, print_train=False):
    """
    训练模型并应用早停机制。

    参数:
        model (torch.nn.Module): 待训练的模型。
        train_loader (torch.utils.data.DataLoader): 训练数据加载器。
        val_loader (torch.utils.data.DataLoader): 验证数据加载器。
        optimizer (torch.optim.Optimizer): 优化器。
        criterion (torch.nn.Module): 损失函数。
        scheduler (torch.optim.lr_scheduler._LRScheduler): 学习率调度器。
        num_epochs (int): 总训练轮数。
        device (torch.device): 设备（CPU或GPU）。
        early_patience (float, optional): 早停耐心值，默认为0.15 * num_epochs。
        print_train: 是否打印训练信息。
    返回:
        torch.nn.Module: 训练好的模型。
        list: 训练过程中每个epoch的平均训练损失列表。
        list: 训练过程中每个epoch的平均验证损失列表。
        int: 早停触发时的epoch数。
    """

    train_loss = []  # 用于记录每个epoch的平均损失
    val_loss = []  # 用于记录验证集上的损失，用于早停判断
    print_frequency = num_epochs / 20  # 计算打印频率

    early_patience_epochs = int(early_patience * num_epochs)  # 早停耐心值（转换为epoch数）
    best_val_loss = float('inf')  # 初始化最佳验证集损失
    early_stop_counter = 0  # 早停计数器

    for epoch in range(num_epochs):
        total_train_loss = 0  # 初始化一个epoch的总损失

        net.train()  # 将模型设置为训练模式
        for i, (datapoints, labels, datapoints_mark, labels_mark) in enumerate(train_loader):
            datapoints, labels, datapoints_mark, labels_mark = datapoints.to(device), labels.to(
                device), datapoints_mark.to(device), labels_mark.to(device)
            optimizer.zero_grad()  # 清空梯度
            
            # 修复数据泄露问题：创建正确的解码器输入
            # 获取批次大小、标签长度和特征维度
            batch_size, seq_len, feature_dim = labels.shape
            label_len = seq_len - length_size  # 计算label_len (前面部分)
            
            # 创建解码器输入 - 使用label_len部分作为起始令牌，并提供第一个预测步的合理起点
            dec_inp = torch.zeros((batch_size, seq_len, feature_dim)).to(device)
            dec_inp[:, :label_len, :] = labels[:, :label_len, :]  # 复制label_len部分作为历史数据
            
            # 对于第一个预测位置，使用最后一个历史点作为起始值，避免全零输入导致的"拉向零中心"效应
            dec_inp[:, label_len:label_len+1, :] = labels[:, label_len-1:label_len, :]
            
            # 创建完整的解码器时间特征输入 - 直接使用实际的未来时间特征
            # 注意：labels_mark 现在包含了未来的时间特征，已在 tslib_data_loader 中修改
            full_labels_mark = labels_mark.clone().to(device)
            
            # 前向传播
            preds = net(datapoints, datapoints_mark, dec_inp, full_labels_mark, None)
            
            # 提取目标部分
            true_labels = labels[:, -length_size:, :]
            
            # 如果需要，去掉最后一个维度以匹配预测
            if preds.shape[-1] == 1 and true_labels.shape[-1] == 1:
                preds = preds.squeeze(-1)
                true_labels = true_labels.squeeze(-1)
            
            loss = criterion(preds, true_labels)  # 计算损失
            loss.backward()  # 反向传播
            optimizer.step()  # 更新模型参数
            total_train_loss += loss.item()  # 累加损失值

        avg_train_loss = total_train_loss / len(train_loader)  # 计算本epoch的平均损失
        train_loss.append(avg_train_loss)  # 记录平均损失

        with torch.no_grad():  # 关闭自动求导以节省内存和提高效率
            total_val_loss = 0
            for val_x, val_y, val_x_mark, val_y_mark in val_loader:
                val_x, val_y, val_x_mark, val_y_mark = val_x.to(device), val_y.to(device), val_x_mark.to(
                    device), val_y_mark.to(device)  # 将数据移到GPU
                
                # 修复数据泄露问题：创建正确的解码器输入
                # 获取批次大小、标签长度和特征维度
                batch_size, seq_len, feature_dim = val_y.shape
                label_len = seq_len - length_size  # 计算label_len (前面部分)
                
                # 创建解码器输入 - 使用label_len部分作为起始令牌，并提供第一个预测步的合理起点
                val_dec_inp = torch.zeros((batch_size, seq_len, feature_dim)).to(device)
                val_dec_inp[:, :label_len, :] = val_y[:, :label_len, :]  # 复制label_len部分作为历史数据
                
                # 对于第一个预测位置，使用最后一个历史点作为起始值，避免全零输入导致的"拉向零中心"效应
                val_dec_inp[:, label_len:label_len+1, :] = val_y[:, label_len-1:label_len, :]
                
                # 创建完整的解码器时间特征输入 - 直接使用实际的未来时间特征
                # 注意：val_y_mark 现在包含了未来的时间特征，已在 tslib_data_loader 中修改
                full_val_y_mark = val_y_mark.clone().to(device)
                
                # 前向传播
                pred_val_y = net(val_x, val_x_mark, val_dec_inp, full_val_y_mark, None)
                
                # 提取目标部分
                true_val_y = val_y[:, -length_size:, :]
                
                # 如果需要，去掉最后一个维度以匹配预测
                if pred_val_y.shape[-1] == 1 and true_val_y.shape[-1] == 1:
                    pred_val_y = pred_val_y.squeeze(-1)
                    true_val_y = true_val_y.squeeze(-1)
                
                val_loss_batch = criterion(pred_val_y, true_val_y)  # 计算损失
                total_val_loss += val_loss_batch.item()

            avg_val_loss = total_val_loss / len(val_loader)  # 计算本epoch的平均验证损失
            val_loss.append(avg_val_loss)  # 记录平均验证损失

            scheduler.step(avg_val_loss)  # 更新学习率（基于当前验证损失）

        # 打印训练信息
        if print_train == True:
            if (epoch + 1) % print_frequency == 0:
                print(f"Epoch: {epoch + 1}, Train Loss: {avg_train_loss:.4f}, Val Loss: {avg_val_loss:.4f}")

        # 早停判断
        if avg_val_loss < best_val_loss:
            best_val_loss = avg_val_loss
            early_stop_counter = 0  # 重置早停计数器
        else:
            early_stop_counter += 1
            if early_stop_counter >= early_patience_epochs:
                print(f'Early stopping triggered at epoch {epoch + 1}.')
                break  # 早停

    net.train()  # 恢复训练模式
    return net, train_loss, val_loss, epoch + 1


# 计算点预测的评估指标
def cal_eval(y_real, y_pred):
    """
    输入参数:
    y_real - numpy数组，表示测试集的真实目标值。
    y_pred - numpy数组，表示预测的结果。

    输出:
    df_eval - pandas DataFrame对象
    """

    y_real, y_pred = np.array(y_real).ravel(), np.array(y_pred).ravel()

    r2 = r2_score(y_real, y_pred)
    mse = mean_squared_error(y_real, y_pred)  # 移除squared参数
    rmse = np.sqrt(mse)  # 手动计算RMSE
    mae = mean_absolute_error(y_real, y_pred)
    mape = mean_absolute_percentage_error(y_real, y_pred) * 100  # Note that dataset cannot have any 0 value.

    df_eval = pd.DataFrame({'R2': r2,
                            'MSE': mse, 'RMSE': rmse,
                            'MAE': mae, 'MAPE': mape},
                        index=['Eval'])

    return df_eval


# 修改load_vmd_modes函数以便更好地控制数据分割
def load_vmd_modes(file_path, train_ratio=0.7):
    """
    加载VMD分解后的多个模态数据，并按比例划分训练集和测试集
    
    参数:
        file_path: VMD模态数据文件路径
        train_ratio: 训练集比例，默认0.7
        
    返回:
        modes_data: 包含所有模态的数据字典
        dates: 日期列表
        train_size: 训练集大小
        df_modes: 原始模态数据DataFrame
    """
    # 读取VMD分解后的数据
    df = pd.read_csv(file_path)
    dates = pd.to_datetime(df['date'])
    
    # 获取所有模态列名
    mode_cols = [col for col in df.columns if col.startswith('mode')]
    
    # 创建数据字典，保存每个模态的数据
    modes_data = {}
    for mode in mode_cols:
        modes_data[mode] = df[mode].values.reshape(-1, 1)
    
    # 计算训练集和测试集大小
    data_length = len(df)
    train_size = int(data_length * train_ratio)
    
    return modes_data, dates, train_size, df


# 新增：针对单个模态训练Informer模型
def train_mode_model(mode_data, train_size, dates, window, length_size, batch_size, num_epochs, device, print_train=False):
    """
    为单个模态训练Informer模型
    
    参数:
        mode_data: 单个模态的数据
        train_size: 训练集大小
        dates: 日期列表，用于生成时间特征
        window: 输入窗口大小
        length_size: 预测长度
        batch_size: 批量大小
        num_epochs: 训练轮数
        device: 设备(CPU/GPU)
        print_train: 是否打印训练过程
        
    返回:
        trained_model: 训练好的模型
        scaler: 用于数据反归一化的缩放器
    """
    # 准备数据标记
    df_stamp = pd.DataFrame({'date': dates})
    data_stamp = time_features(df_stamp, timeenc=1, freq='min')
    
    # 数据归一化
    scaler = MinMaxScaler()
    data_normalized = scaler.fit_transform(mode_data)
    
    # 划分训练集和测试集
    data_train = data_normalized[:train_size]
    data_train_mark = data_stamp[:train_size]
    data_test = data_normalized[train_size:]
    data_test_mark = data_stamp[train_size:]
    
    # 准备数据加载器
    train_loader, x_train, y_train, x_train_mark, y_train_mark = tslib_data_loader(
        window, length_size, batch_size, data_train, data_train_mark)
    
    test_loader, x_test, y_test, x_test_mark, y_test_mark = tslib_data_loader(
        window, length_size, batch_size, data_test, data_test_mark)
    
    # 配置模型
    config = Config()
    config.enc_in = 1  # 输入特征为1
    config.dec_in = 1
    config.c_out = 1
    
    # 初始化模型
    model = Informer.Model(config).to(device)
    
    # 训练模型
    criterion = nn.MSELoss().to(device)
    optimizer = optim.Adam(
        model.parameters(), 
        lr=config.learning_rate,
        weight_decay=config.weight_decay  # 添加L2正则化
    )
    
    trained_model, train_loss, final_epoch = model_train(
        model, train_loader, length_size, optimizer, criterion, num_epochs, device, print_train)
    
    return trained_model, scaler, (x_test, y_test, x_test_mark, y_test_mark)


# 修改vmd_informer_predict函数，使用准确的索引对齐
def vmd_informer_predict(modes_file, output_file, train_ratio=0.7, seed=42):
    """
    完整的VMD-TCN-Informer预测流程
    
    参数:
        modes_file: VMD分解模态文件路径
        output_file: 输出结果文件路径
        train_ratio: 训练集比例，默认0.7
        seed: 随机种子，默认42
    """
    # 设置随机种子，确保结果可重现
    set_seed(seed)
    
    print("开始VMD-TCN-Informer多模态预测...")
    
    # 加载VMD分解后的数据
    modes_data, dates, train_size, df_modes = load_vmd_modes(modes_file, train_ratio)
    
    # 准备原始数据进行比较
    original_data = pd.read_csv('data/pack_mean_total_data.csv')
    original_data['date'] = pd.to_datetime(original_data['date'].str.strip())
    
    # 确保日期格式一致
    df_modes['date'] = pd.to_datetime(df_modes['date'])
    
    # 统一配置
    config = Config()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 确保测试集的日期和原始数据能够对应
    test_dates = dates[train_size:].reset_index(drop=True)
    print(f"测试集大小: {len(test_dates)}个数据点")
    
    # 为保存分量预测效果图创建目录
    mode_plots_dir = os.path.dirname(output_file) + "/mode_plots"
    os.makedirs(mode_plots_dir, exist_ok=True)
    
    # 创建一个大图用于展示所有模态的预测结果
    n_modes = len(modes_data)
    fig_all_modes = plt.figure(figsize=(15, 4 * n_modes))
    
    # 为每个模态训练单独的模型并进行预测
    mode_idx = 1
    mode_predictions = {}
    test_start_indices = {}  # 存储每个模态测试集的起始索引
    
    for mode_idx, (mode, data) in enumerate(modes_data.items()):
        print(f"\n训练{mode}模型...")
        
        # 为每个模态设置不同但固定的种子，确保可重现性
        mode_seed = seed + mode_idx  # 基础种子加上模态索引
        set_seed(mode_seed)
        print(f"{mode}模型使用随机种子: {mode_seed}")
        
        # 分割训练集和测试集
        data_train = data[:train_size]
        data_test = data[train_size:]
        
        # 准备数据标记
        df_stamp = pd.DataFrame({'date': dates})
        data_stamp = time_features(df_stamp, timeenc=1, freq='min')
        data_train_mark = data_stamp[:train_size]
        data_test_mark = data_stamp[train_size:]
        
        # 数据归一化 - 确保使用训练集的统计特性进行标准化
        scaler = MinMaxScaler(feature_range=(-1, 1))  # 调整归一化范围，避免归一化后的数据过于接近
        data_train_norm = scaler.fit_transform(data_train)
        data_test_norm = scaler.transform(data_test)
        
        # 使用统一的配置调用数据加载器
        train_loader, x_train, y_train, x_train_mark, y_train_mark, _ = tslib_data_loader(
            config.seq_len, config.pred_len, config.batch_size, 
            data_train_norm, data_train_mark
        )
        
        test_loader, x_test, y_test, x_test_mark, y_test_mark, test_start_idx = tslib_data_loader(
            config.seq_len, config.pred_len, config.batch_size, 
            data_test_norm, data_test_mark
        )
        
        # 记录测试集起始索引
        test_start_indices[mode] = test_start_idx
        
        # 配置模型
        config = Config()
        config.enc_in = 1
        config.dec_in = 1
        config.c_out = 1
        
        # 初始化TCN-Informer混合模型
        model = TCN_Informer_Model(config).to(device)
        print(f"使用TCN-Informer混合模型训练{mode}数据")
        
        # 训练模型
        criterion = nn.MSELoss().to(device)
        optimizer = optim.Adam(
            model.parameters(), 
            lr=config.learning_rate,
            weight_decay=config.weight_decay  # 添加L2正则化
        )
        
        # 使用统一的配置调用模型训练函数
        trained_model, train_loss, final_epoch = model_train(
            model, train_loader, config.pred_len, optimizer, criterion, config.num_epochs, device, print_train=True)
        
        # 进行预测 - 修改这部分以防止数据泄露
        trained_model.eval()
        with torch.no_grad():
            # 使用滑动窗口预测，以避免数据泄露
            all_predictions = []
            
            for i in range(len(x_test)):
                # 获取单个样本
                x_sample = x_test[i:i+1].to(device)
                x_mark_sample = x_test_mark[i:i+1].to(device)
                y_mark_sample = y_test_mark[i:i+1].to(device)
                
                # 获取当前样本的最后一个时间点
                current_date_idx = train_size + test_start_idx + i
                if current_date_idx < len(dates):
                    current_date = dates[current_date_idx]
                    
                    # 生成准确的未来时间特征
                    _, future_features = generate_future_time_features(
                        current_date, config.pred_len, freq='min'
                    )
                    
                    # 转换为张量
                    future_features = torch.tensor(future_features).type(torch.float32).to(device)
                    
                    # 创建解码器输入 - 使用历史数据作为起始令牌，并提供第一个预测步的合理起点
                    batch_size = 1
                    seq_len = config.label_len + config.pred_len
                    dec_inp = torch.zeros((batch_size, seq_len, config.dec_in)).to(device)
                    
                    # 填充历史数据部分作为起始令牌
                    dec_inp[:, :config.label_len, :] = x_sample[:, -config.label_len:, :]
                    
                    # 对于第一个预测位置，使用最后一个历史点作为起始值，避免全零输入导致的"拉向零中心"效应
                    dec_inp[:, config.label_len:config.label_len+1, :] = x_sample[:, -1:, :]
                    
                    # 创建解码器时间特征
                    dec_mark_inp = torch.zeros((batch_size, seq_len, y_mark_sample.shape[-1])).to(device)
                    
                    # 填充起始令牌部分的时间特征 - 确保形状匹配
                    # 检查y_mark_sample的维度并进行适当处理
                    if len(y_mark_sample.shape) == 2:  # 如果y_mark_sample是二维的 [seq_len, feature]
                        # 取前config.label_len行或可用的最大行数
                        max_len = min(config.label_len, y_mark_sample.shape[0])
                        dec_mark_inp[0, :max_len, :] = y_mark_sample[:max_len]
                    else:  # 如果y_mark_sample是三维的 [batch, seq_len, feature]
                        # 取前config.label_len列或可用的最大列数
                        max_len = min(config.label_len, y_mark_sample.shape[1])
                        dec_mark_inp[:, :max_len, :] = y_mark_sample[:, :max_len, :]
                    
                    # 填充预测部分的时间特征 - 使用准确生成的未来时间特征
                    for j in range(min(config.pred_len, len(future_features))):
                        dec_mark_inp[:, config.label_len + j, :] = future_features[j:j+1]
                else:
                    # 如果超出日期范围，则使用常规方法
                    batch_size = 1
                    seq_len = config.label_len + config.pred_len
                    
                    # 创建解码器输入 - 使用历史数据作为起始令牌，并提供合理的预测起点
                    dec_inp = torch.zeros((batch_size, seq_len, config.dec_in)).to(device)
                    dec_inp[:, :config.label_len, :] = x_sample[:, -config.label_len:, :]
                    
                    # 对于第一个预测位置，使用最后一个历史点作为起始值，避免全零输入导致的"拉向零中心"效应
                    dec_inp[:, config.label_len:config.label_len+1, :] = x_sample[:, -1:, :]
                    
                    # 创建解码器时间特征
                    dec_mark_inp = torch.zeros((batch_size, seq_len, y_mark_sample.shape[-1])).to(device)
                    dec_mark_inp[:, :config.label_len, :] = y_mark_sample
                
                # 进行预测
                pred = trained_model(x_sample, x_mark_sample, dec_inp, dec_mark_inp)
                all_predictions.append(pred.cpu())
            
            # 合并所有预测结果
            if all_predictions:
                pred = torch.cat(all_predictions, dim=0)
            else:
                continue
        
        # 取出预测结果
        pred = pred.detach().cpu()
        
        # 反归一化 - 使用与归一化相同的scaler
        pred_inverse = scaler.inverse_transform(pred.reshape(-1, 1))
        
        # 保存该模态的预测结果
        mode_predictions[mode] = pred_inverse
        
        # 获取真实值 - 确保预测与实际值精确对齐
        # 真实测试数据需要从window索引开始，这是因为预测是基于前window个点来预测下一个点
        offset = test_start_idx  # 窗口大小，从这个索引开始的点才是我们真正预测的点
        mode_true = data_test[offset:offset+len(pred_inverse)]
        
        # 确保预测值和真实值长度一致
        min_len = min(len(mode_true), len(pred_inverse))
        mode_true = mode_true[:min_len]
        mode_pred = pred_inverse[:min_len]
        
        # 确保日期对应正确
        if offset + min_len <= len(test_dates):
            matched_dates = test_dates[offset:offset+min_len]
        else:
            # 处理可能超出边界的情况
            matched_dates = test_dates[offset:len(test_dates)]
            # 调整预测和真实值的长度
            mode_true = mode_true[:len(matched_dates)]
            mode_pred = mode_pred[:len(matched_dates)]
        
        print(f"{mode}预测长度: {len(mode_pred)}, 真实值长度: {len(mode_true)}")
        print(f"对齐点: 测试集索引从{offset}开始")
        
        # 计算此模态的评估指标
        mode_eval = cal_eval(mode_true, mode_pred)
        print(f"\n{mode}预测评估指标:")
        print(mode_eval)
        
        # 绘制单个模态的预测效果图
        plt.figure(figsize=(12, 5))
        plt.plot(matched_dates, mode_true, 'r-', label='真实值', linewidth=1.5)
        plt.plot(matched_dates, mode_pred, 'b-', label='预测值', linewidth=1.5)
        plt.title(f'{mode} 预测结果 (精确对齐)', fontproperties=font_prop if 'font_prop' in locals() else None)
        plt.legend(prop=font_prop if 'font_prop' in locals() else None)
        plt.grid(True)
        plt.tight_layout()
        
        # 保存此模态的预测效果图
        mode_plot_file = f"{mode_plots_dir}/{mode}_prediction.png"
        plt.savefig(mode_plot_file, dpi=300)
        plt.close()
        
        # 添加到大图中
        ax = fig_all_modes.add_subplot(n_modes, 1, mode_idx + 1)
        ax.plot(matched_dates, mode_true, 'r-', label='真实值', linewidth=1.5)
        ax.plot(matched_dates, mode_pred, 'b-', label='预测值', linewidth=1.5)
        ax.set_title(f'{mode} 预测结果 (精确对齐)', fontproperties=font_prop if 'font_prop' in locals() else None)
        ax.legend(prop=font_prop if 'font_prop' in locals() else None)
        ax.grid(True)
        
        mode_idx += 1
    
    # 保存所有模态的预测效果图
    fig_all_modes.tight_layout()
    all_modes_plot_file = f"{mode_plots_dir}/all_modes_prediction.png"
    fig_all_modes.savefig(all_modes_plot_file)
    plt.close(fig_all_modes)
    
    # 将各模态预测结果相加得到最终预测
    print("\n合并各模态预测结果...")
    
    # 计算所有模态共同的起始偏移量
    common_offset = max(test_start_indices.values())
    
    # 调整各模态的预测以使用相同的起始点
    aligned_predictions = {}
    for mode, pred in mode_predictions.items():
        mode_offset = test_start_indices[mode]
        # 计算此模态与共同偏移量的差距
        diff = common_offset - mode_offset
        
        if diff > 0:
            # 如果此模态的起始点早于共同起始点，则需要截掉前面的点
            aligned_predictions[mode] = pred[diff:]
        else:
            # 如果此模态的起始点晚于或等于共同起始点，则保持不变
            aligned_predictions[mode] = pred
    
    # 找出所有对齐后预测结果中最短的长度
    min_length = min([len(pred) for pred in aligned_predictions.values()])
    
    # 初始化最终预测结果
    final_pred = np.zeros((min_length, 1))
    
    # 合并所有模态预测结果
    for mode, pred in aligned_predictions.items():
        final_pred += pred[:min_length]
    
    # 获取对应的真实值 - 从原始数据中读取
    test_merged = pd.merge(original_data, df_modes[['date']], on='date', how='inner')
    
    # 考虑共同偏移量，获取训练集之后的数据
    true_start_idx = train_size + common_offset
    if true_start_idx + min_length <= len(test_merged):
        true_values = test_merged['Target'].iloc[true_start_idx:true_start_idx+min_length].values.reshape(-1, 1)
        test_dates = pd.to_datetime(test_merged['date'].iloc[true_start_idx:true_start_idx+min_length])
    else:
        # 处理边界情况
        available_len = len(test_merged) - true_start_idx
        true_values = test_merged['Target'].iloc[true_start_idx:true_start_idx+available_len].values.reshape(-1, 1)
        test_dates = pd.to_datetime(test_merged['date'].iloc[true_start_idx:true_start_idx+available_len])
        # 调整预测结果长度
        final_pred = final_pred[:available_len]
    
    print(f"最终预测结果长度: {len(final_pred)}, 真实值长度: {len(true_values)}")
    
    # 计算评估指标
    df_eval = cal_eval(true_values, final_pred)
    print("\n总体预测评估指标:")
    print(df_eval)
    
    # 创建结果DataFrame
    result_df = pd.DataFrame({
        '日期': test_dates,
        '真实值': true_values.flatten(),
        '预测值': final_pred.flatten()
    })
    
    # 保存结果
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    result_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n预测结果已保存到: {output_file}")
    print(f"各分量预测效果图已保存到: {mode_plots_dir}")
    
    # 绘制总体预测结果
    plt.figure(figsize=(12, 6))
    plt.plot(result_df['日期'], result_df['真实值'], 'r-', label='真实值', linewidth=2)
    plt.plot(result_df['日期'], result_df['预测值'], 'b-', label='预测值', linewidth=2)
    plt.title('VMD-TCN-Informer总体预测结果 (精确对齐)', fontproperties=font_prop if 'font_prop' in locals() else None)
    plt.legend(prop=font_prop if 'font_prop' in locals() else None)
    plt.grid(True)
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(output_file.replace('.csv', '.png'), dpi=300)
    plt.close()  # 关闭图形，避免在非交互环境中显示
    
    return df_eval, result_df


# 添加准确生成未来时间特征的函数
def generate_future_time_features(last_date, pred_len, freq='min'):
    """
    准确生成未来时间点的时间特征
    
    参数:
        last_date: 最后一个已知时间点
        pred_len: 需要预测的时间步长
        freq: 时间频率，默认为分钟('min')
        
    返回:
        future_dates: 未来时间点的日期列表
        future_features: 未来时间点的时间特征
    """
    from pandas.tseries.frequencies import to_offset
    import pandas as pd
    
    # 确定时间间隔
    if isinstance(last_date, str):
        last_date = pd.to_datetime(last_date)
    
    # 生成未来时间点
    future_dates = []
    delta = to_offset(freq)
    
    current_date = last_date
    for i in range(pred_len):
        current_date = current_date + delta
        future_dates.append(current_date)
    
    # 转换为DataFrame以便生成时间特征
    future_df = pd.DataFrame({'date': future_dates})
    
    # 生成时间特征
    future_features = time_features(future_df, timeenc=1, freq=freq)
    
    return future_dates, future_features


# 3. 恢复 Config 类的原始状态或保持其一致性
# (我们将保持上次修复中的单步预测配置)
class Config:
    def __init__(self):
        # basic
        self.seq_len = 20          # 输入序列长度 (与 window=10 对应)
        self.label_len = 10        # 修改为10，与y_test_mark的第二维大小匹配
        self.pred_len = 1            # 预测序列长度 (关键修改: 从24改为1)
        self.freq = 'min'              # 时间的频率
        # 模型训练
        self.batch_size = 512  # 批次大小
        self.num_epochs = 200  # 训练的轮数
        self.learning_rate = 0.001  # 学习率
        self.weight_decay = 1e-4  # L2正则化系数
        self.stop_ratio = 0.15  # 早停的比例，默认为训练轮数的15%
        # 模型 define
        self.dec_in = 1  # 解码器输入特征数量, 输入几个变量就是几
        self.enc_in = 1  # 编码器输入特征数量
        self.c_out = 1  # 输出维度##########这个很重要
        # 模型超参数
        self.d_model = 64  # 模型维度
        self.n_heads = 8  # 多头注意力头数
        self.dropout = 0.2  # 丢弃率
        self.e_layers = 2  # 编码器块的数量
        self.d_layers = 1  # 解码器块的数量
        self.d_ff = 128  # 全连接网络维度
        self.factor = 10  # 注意力因子
        self.activation = 'gelu'  # 激活函数
        self.channel_independence = 0  # 频道独立性，0:频道依赖，1:频道独立

        self.top_k = 9  # TimesBlock中的参数
        self.num_kernels = 6  # Inception中的参数
        self.distil = True # 注意：原文为1，布尔值更规范
        # 一般不需要动的参数
        self.embed = 'timeF'  # 时间特征编码方式
        self.output_attention = False # 注意：原文为0，布尔值更规范
        self.task_name = 'short_term_forecast'  # 模型的任务，一般不动但是必须这个参数

        # TCN参数设置 - 针对多尺度局部特征提取优化
        self.tcn_layers = 6  # 减少TCN层数，防止过度拟合
        self.tcn_kernel_size = 9  # 减小卷积核尺寸，更精细地捕捉局部特征
        self.tcn_dropout = 0.1  # 适度增加dropout，防止过拟合
        
        # 新增: 残差连接控制
        self.use_residual = True  # 是否使用残差连接
        self.residual_weight = 0.1  # 残差连接权重


# 主函数：运行VMD-Informer预测
if __name__ == "__main__":
    # 设置文件路径
    vmd_modes_file = 'results/csv/vmd_modes.csv'
    output_file = 'results/vmd_tcn_informer_predictions.csv'
    
    # 设置随机种子
    SEED = 42
    
    # 配置实验参数
    config = Config()
    
    # 残差连接配置
    config.use_residual = True      # 是否使用残差连接
    config.residual_weight = 0.1    # 残差权重，较小的值会保留更多极值特征
    
    # 训练配置
    config.learning_rate = 0.001    # 学习率
    config.weight_decay = 1e-4      # L2正则化系数
    config.stop_ratio = 0.15        # 早停耐心值，占总训练轮数的比例
    config.num_epochs = 200         # 最大训练轮数
    
    print("开始执行VMD-TCN-Informer集成预测模型...")
    print(f"随机种子: {SEED}")
    print(f"训练配置: 学习率={config.learning_rate}, L2正则化={config.weight_decay}")
    print(f"残差连接配置: 启用={config.use_residual}, 权重={config.residual_weight}")
    print(f"早停配置: 耐心值比例={config.stop_ratio}, 最大轮数={config.num_epochs}, 早停触发轮数={int(config.stop_ratio * config.num_epochs)}")
    
    # 执行VMD-TCN-Informer预测
    df_eval, result_df = vmd_informer_predict(
        vmd_modes_file, 
        output_file, 
        train_ratio=0.8,
        seed=SEED
    )