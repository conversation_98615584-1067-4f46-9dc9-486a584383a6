import numpy as np

__all__ = ['initialization']

def initialization(N, dim, ub, lb):
    """
    Initialize the population of search agents.

    Parameters:
    - N: number of agents (int)
    - dim: problem dimensionality (int)
    - ub: upper bound, scalar or array-like of length dim
    - lb: lower bound, scalar or array-like of length dim

    Returns:
    - x: initialized population array of shape (N, dim)
    - new_lb: array of lower bounds for each dimension (length dim)
    - new_ub: array of upper bounds for each dimension (length dim)
    """
    # Convert bounds to numpy arrays
    ub_arr = np.array(ub, dtype=float)
    lb_arr = np.array(lb, dtype=float)

    # Determine boundary dimension
    if ub_arr.ndim == 0:
        # Single scalar bound for all dimensions
        new_lb = np.full(dim, lb_arr)
        new_ub = np.full(dim, ub_arr)
        x = np.random.rand(N, dim) * (ub_arr - lb_arr) + lb_arr
    else:
        # Individual bounds per dimension
        if ub_arr.size != dim or lb_arr.size != dim:
            raise ValueError(f"Length of ub and lb must be 1 or equal to dim={dim}")
        new_lb = lb_arr.copy()
        new_ub = ub_arr.copy()
        # Initialize each dimension separately
        x = np.zeros((N, dim), dtype=float)
        for i in range(dim):
            x[:, i] = np.random.rand(N) * (ub_arr[i] - lb_arr[i]) + lb_arr[i]

    return x, new_lb, new_ub
