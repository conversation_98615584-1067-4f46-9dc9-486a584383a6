# 文件：utils/plotting.py
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
from utils.functions_details import get_functions_details

def func_plot(func_name):
    """
    在 3D 空间中画出测试函数 func_name 的表面图。

    参数:
      func_name: 字符串，如 'F1','F2',…,'F23'，同 MATLAB 版。
    """
    # 取出下界、上界、维度和目标函数句柄
    lb, ub, dim, fobj = get_functions_details(func_name)

    # 根据 func_name 选 x,y 范围
    if func_name in {'F1','F2','F3','F4','F6','F14'}:
        x = np.arange(-100, 100+1e-9, 2)
    elif func_name == 'F5':
        x = np.arange(-200, 200+1e-9, 2)
    elif func_name == 'F7':
        x = np.arange(-1, 1+1e-9, 0.03)
    elif func_name == 'F8':
        x = np.arange(-500, 500+1e-9, 10)
    elif func_name == 'F9':
        x = np.arange(-5, 5+1e-9, 0.1)
    elif func_name == 'F10':
        x = np.arange(-20, 20+1e-9, 0.5)
    elif func_name == 'F11':
        x = np.arange(-500, 500+1e-9, 10)
    elif func_name == 'F12':
        x = np.arange(-10, 10+1e-9, 0.1)
    elif func_name == 'F13':
        x = np.arange(-5, 5+1e-9, 0.08)
    elif func_name in {'F15','F17','F19','F20','F21','F22','F23'}:
        x = np.arange(-5, 5+1e-9, 0.1)
    elif func_name == 'F16':
        x = np.arange(-1, 1+1e-9, 0.01)
    elif func_name == 'F18':
        x = np.arange(-5, 5+1e-9, 0.06)
    else:
        raise ValueError(f"Unknown function name: {func_name}")

    # 构造网格
    X, Y = np.meshgrid(x, x)  # y 与 x 范围相同
    Z = np.zeros_like(X)

    # 计算函数值
    L = x.size
    for i in range(L):
        for j in range(L):
            xi = X[i, j]
            yi = Y[i, j]
            # 不同函数维度不一样，需要补 0
            if func_name == 'F15':
                args = [xi, yi, 0, 0]
            elif func_name == 'F19':
                args = [xi, yi, 0]
            elif func_name == 'F20':
                args = [xi, yi, 0, 0, 0, 0]
            elif func_name in {'F21','F22','F23'}:
                args = [xi, yi, 0, 0]
            else:
                args = [xi, yi]
            Z[i, j] = fobj(np.array(args))

    # 绘图
    fig = plt.figure(figsize=(8, 6))
    ax = fig.add_subplot(111, projection='3d')
    # 面
    ax.plot_surface(X, Y, Z, linewidth=0, antialiased=True)
    # 底部等高线
    ax.contourf(X, Y, Z, zdir='z', offset=Z.min(), alpha=0.7)
    ax.set_xlabel('x')
    ax.set_ylabel('y')
    ax.set_zlabel('f(x,y)')
    ax.set_title(f"Surface of {func_name}")
    plt.tight_layout()
    plt.show()
