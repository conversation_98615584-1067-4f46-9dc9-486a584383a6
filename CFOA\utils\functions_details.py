import numpy as np

__all__ = ['get_functions_details']


def Ufun(x, a, k, m):
    """
    Penalty function used in F12 and F13
    """
    x = np.asarray(x)
    return k * np.where(x > a, (x - a) ** m, 0) + k * np.where(x < -a, (-x - a) ** m, 0)

# Benchmark functions F1 to F23

def F1(x):
    x = np.asarray(x)
    return np.sum(x ** 2)


def F2(x):
    x = np.asarray(x)
    return np.sum(np.abs(x)) + np.prod(np.abs(x))


def F3(x):
    x = np.asarray(x)
    cumsum = np.cumsum(x)
    return np.sum(cumsum ** 2)


def F4(x):
    x = np.asarray(x)
    return np.max(np.abs(x))


def F5(x):
    x = np.asarray(x)
    return np.sum(100 * (x[1:] - x[:-1] ** 2) ** 2 + (x[:-1] - 1) ** 2)


def F6(x):
    x = np.asarray(x)
    return np.sum((np.abs(x + 0.5)) ** 2)


def F7(x):
    x = np.asarray(x)
    dim = x.size
    return np.sum(np.arange(1, dim + 1) * (x ** 4)) + np.random.rand()


def F8(x):
    x = np.asarray(x)
    return np.sum(-x * np.sin(np.sqrt(np.abs(x))))


def F9(x):
    x = np.asarray(x)
    dim = x.size
    return np.sum(x ** 2 - 10 * np.cos(2 * np.pi * x)) + 10 * dim


def F10(x):
    x = np.asarray(x)
    dim = x.size
    term1 = -20 * np.exp(-0.2 * np.sqrt(np.sum(x ** 2) / dim))
    term2 = -np.exp(np.sum(np.cos(2 * np.pi * x)) / dim)
    return term1 + term2 + 20 + np.e


def F11(x):
    x = np.asarray(x)
    dim = x.size
    return np.sum(x ** 2) / 4000 - np.prod(np.cos(x / np.sqrt(np.arange(1, dim + 1)))) + 1


def F12(x):
    x = np.asarray(x)
    dim = x.size
    term1 = (np.pi / dim) * (10 * np.sin(np.pi * (1 + (x[0] + 1) / 4)) ** 2)
    term2 = np.sum((((x[:-1] + 1) / 4) ** 2) * (1 + 10 * np.sin(np.pi * (1 + (x[1:] + 1) / 4)) ** 2))
    term3 = ((x[-1] + 1) / 4) ** 2
    return term1 + term2 + term3 + np.sum(Ufun(x, 10, 100, 4))


def F13(x):
    x = np.asarray(x)
    dim = x.size
    term1 = 0.1 * (
        np.sin(3 * np.pi * x[0]) ** 2 +
        np.sum((x[:-1] - 1) ** 2 * (1 + np.sin(3 * np.pi * x[1:]) ** 2)) +
        ((x[-1] - 1) ** 2) * (1 + np.sin(2 * np.pi * x[-1]) ** 2)
    )
    return term1 + np.sum(Ufun(x, 5, 100, 4))


def F14(x):
    x = np.asarray(x)
    aS = np.array([
        [-32, -16,   0,  16,  32, -32, -16,   0,  16,  32,
         -32, -16,   0,  16,  32, -32, -16,   0,  16,  32,
         -32, -16,   0,  16,  32],
        [-32, -32, -32, -32, -32, -16, -16, -16, -16, -16,
           0,   0,   0,   0,   0,  16,  16,  16,  16,  16,
          32,  32,  32,  32,  32]
    ])
    bS = np.array([np.sum((x - aS[:, j]) ** 6) for j in range(25)])
    return (1 / 500 + np.sum(1 / (np.arange(1, 26) + bS))) ** -1


def F15(x):
    x = np.asarray(x)
    aK = np.array([.1957, .1947, .1735, .16, .0844, .0627, .0456, .0342, .0323, .0235, .0246])
    bK = 1 / np.array([.25, .5, 1, 2, 4, 6, 8, 10, 12, 14, 16])
    return np.sum((aK - (x[0] * (bK**2 + x[1] * bK) / (bK**2 + x[2] * bK + x[3])))**2)


def F16(x):
    x = np.asarray(x)
    return (4 * x[0]**2 - 2.1 * x[0]**4 + x[0]**6 / 3 + x[0] * x[1]
            - 4 * x[1]**2 + 4 * x[1]**4)


def F17(x):
    x = np.asarray(x)
    return ((x[1] - (x[0]**2)*5.1/(4*np.pi**2) + 5/np.pi*x[0] - 6)**2
            + 10*(1 - 1/(8*np.pi))*np.cos(x[0]) + 10)


def F18(x):
    x = np.asarray(x)
    return ((1 + (x[0] + x[1] + 1)**2 * (19 - 14*x[0] + 3*x[0]**2 - 14*x[1]
            + 6*x[0]*x[1] + 3*x[1]**2))
            * (30 + (2*x[0] - 3*x[1])**2 * (18 - 32*x[0] + 12*x[0]**2
            + 48*x[1] - 36*x[0]*x[1] + 27*x[1]**2)))


def F19(x):
    x = np.asarray(x)
    aH = np.array([[3, 10, 30], [0.1, 10, 35], [3, 10, 30], [0.1, 10, 35]])
    cH = np.array([1, 1.2, 3, 3.2])
    pH = np.array([.3689, .117, .2673, .4699, .4387, .747,
                   .1091, .8732, .5547, .03815, .5743, .8828]).reshape(4,3)
    o = 0
    for i in range(4):
        o -= cH[i] * np.exp(-np.sum(aH[i] * ((x - pH[i])**2)))
    return o


def F20(x):
    x = np.asarray(x)
    aH = np.array([[10, 3, 17, 3.5, 1.7, 8], [0.05, 10, 17, 0.1, 8, 14],
                   [3, 3.5, 1.7, 10, 17, 8], [17, 8, 0.05, 10, 0.1, 14]])
    cH = np.array([1, 1.2, 3, 3.2])
    pH = np.array([.1312, .1696, .5569, .0124, .8283, .5886,
                   .2329, .4135, .8307, .3736, .1004, .9991,
                   .2348, .1415, .3522, .2883, .3047, .6650,
                   .4047, .8828, .8732, .5743, .1091, .0381]).reshape(4,6)
    o = 0
    for i in range(4):
        o -= cH[i] * np.exp(-np.sum(aH[i] * ((x - pH[i])**2)))
    return o


def F21(x):
    x = np.asarray(x)
    aSH = np.array([
        [4,4,4,4],[1,1,1,1],[8,8,8,8],[6,6,6,6],[3,7,3,7],
        [2,9,2,9],[5,5,3,3],[8,1,8,1],[6,2,6,2],[7,3.6,7,3.6]
    ])
    cSH = np.array([.1,.2,.2,.4,.4,.6,.3,.7,.5,.5])
    o = 0
    for i in range(5):
        diff = x - aSH[i]
        o -= (np.dot(diff, diff) + cSH[i])**-1
    return o


def F22(x):
    x = np.asarray(x)
    aSH = np.array([
        [4,4,4,4],[1,1,1,1],[8,8,8,8],[6,6,6,6],[3,7,3,7],
        [2,9,2,9],[5,5,3,3],[8,1,8,1],[6,2,6,2],[7,3.6,7,3.6]
    ])
    cSH = np.array([.1,.2,.2,.4,.4,.6,.3,.7,.5,.5])
    o = 0
    for i in range(7):
        diff = x - aSH[i]
        o -= (np.dot(diff, diff) + cSH[i])**-1
    return o


def F23(x):
    x = np.asarray(x)
    aSH = np.array([
        [4,4,4,4],[1,1,1,1],[8,8,8,8],[6,6,6,6],[3,7,3,7],
        [2,9,2,9],[5,5,3,3],[8,1,8,1],[6,2,6,2],[7,3.6,7,3.6]
    ])
    cSH = np.array([.1,.2,.2,.4,.4,.6,.3,.7,.5,.5])
    o = 0
    for i in range(10):
        diff = x - aSH[i]
        o -= (np.dot(diff, diff) + cSH[i])**-1
    return o


def get_functions_details(F):
    """
    Returns lb, ub, dim, fobj corresponding to benchmark function F.
    """
    # Default single-value lb/ub to arrays if dim>1
    mapping = {
        'F1':  {'lb': -100,     'ub': 100,      'dim': 30, 'fobj': F1},
        'F2':  {'lb': -10,      'ub': 10,       'dim': 30, 'fobj': F2},
        'F3':  {'lb': -100,     'ub': 100,      'dim': 30, 'fobj': F3},
        'F4':  {'lb': -100,     'ub': 100,      'dim': 30, 'fobj': F4},
        'F5':  {'lb': -30,      'ub': 30,       'dim': 30, 'fobj': F5},
        'F6':  {'lb': -100,     'ub': 100,      'dim': 30, 'fobj': F6},
        'F7':  {'lb': -1.28,    'ub': 1.28,     'dim': 30, 'fobj': F7},
        'F8':  {'lb': -500,     'ub': 500,      'dim': 30, 'fobj': F8},
        'F9':  {'lb': -5.12,    'ub': 5.12,     'dim': 30, 'fobj': F9},
        'F10': {'lb': -32,      'ub': 32,       'dim': 30, 'fobj': F10},
        'F11': {'lb': -600,     'ub': 600,      'dim': 30, 'fobj': F11},
        'F12': {'lb': -50,      'ub': 50,       'dim': 30, 'fobj': F12},
        'F13': {'lb': -50,      'ub': 50,       'dim': 30, 'fobj': F13},
        'F14': {'lb': -65.536,  'ub': 65.536,   'dim': 2,  'fobj': F14},
        'F15': {'lb': -5,       'ub': 5,        'dim': 4,  'fobj': F15},
        'F16': {'lb': -5,       'ub': 5,        'dim': 2,  'fobj': F16},
        'F17': {'lb': [-5, 0],  'ub': [10, 15],  'dim': 2,  'fobj': F17},
        'F18': {'lb': -2,       'ub': 2,        'dim': 2,  'fobj': F18},
        'F19': {'lb': 0,        'ub': 1,        'dim': 3,  'fobj': F19},
        'F20': {'lb': 0,        'ub': 1,        'dim': 6,  'fobj': F20},
        'F21': {'lb': 0,        'ub': 10,       'dim': 4,  'fobj': F21},
        'F22': {'lb': 0,        'ub': 10,       'dim': 4,  'fobj': F22},
        'F23': {'lb': 0,        'ub': 10,       'dim': 4,  'fobj': F23},
    }
    if F not in mapping:
        raise ValueError(f"Unknown function '{F}'")
    cfg = mapping[F]
    lb = np.array(cfg['lb']) if np.iterable(cfg['lb']) else cfg['lb']
    ub = np.array(cfg['ub']) if np.iterable(cfg['ub']) else cfg['ub']
    return lb, ub, cfg['dim'], cfg['fobj']
