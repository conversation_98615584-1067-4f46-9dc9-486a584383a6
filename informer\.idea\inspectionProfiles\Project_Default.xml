<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="14">
            <item index="0" class="java.lang.String" itemvalue="torchvision" />
            <item index="1" class="java.lang.String" itemvalue="sklearn" />
            <item index="2" class="java.lang.String" itemvalue="tqdm" />
            <item index="3" class="java.lang.String" itemvalue="protobuf" />
            <item index="4" class="java.lang.String" itemvalue="seaborn" />
            <item index="5" class="java.lang.String" itemvalue="tensorboard" />
            <item index="6" class="java.lang.String" itemvalue="geotorch" />
            <item index="7" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="8" class="java.lang.String" itemvalue="matplotlib" />
            <item index="9" class="java.lang.String" itemvalue="torch" />
            <item index="10" class="java.lang.String" itemvalue="torchaudio" />
            <item index="11" class="java.lang.String" itemvalue="tables" />
            <item index="12" class="java.lang.String" itemvalue="future" />
            <item index="13" class="java.lang.String" itemvalue="statsmodels" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>