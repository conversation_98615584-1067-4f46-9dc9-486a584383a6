# 文件：utils/cfoa.py
import numpy as np

def initialization(num_agents, dim, ub, lb):
    """
    对应 MATLAB 里的 initialization(SearchAgents_no, dim, ub, lb)
    随机均匀初始化 Fisher 群体在下界/上界之间
    """
    lb = np.asarray(lb)
    ub = np.asarray(ub)
    # Fisher.shape == (num_agents, dim)
    return lb + (ub - lb) * np.random.rand(num_agents, dim)

def cfoa(SearchAgents_no, Max_EFs, lb, ub, dim, fobj):
    """
    Python 版 Catch Fish Optimization Algorithm

    参数:
      SearchAgents_no: 群体规模
      Max_EFs: 最大函数评估次数
      lb, ub: 下界、上界 (长度 dim 的可迭代)
      dim: 维度
      fobj: 目标函数，签名 fobj(x: 1D array) -> float

    返回:
      Best_pos: 全局最优解 (长度 dim 的 1D array)
      Best_score: 全局最优目标值 (float)
      cg_curve: 每次评估后的最佳值历史 (长度 Max_EFs 的 1D array)
    """
    lb = np.asarray(lb)
    ub = np.asarray(ub)
    # 初始化
    Fisher = initialization(SearchAgents_no, dim, ub, lb)
    newFisher = Fisher.copy()
    EFs = 0
    Best_score = np.inf
    Best_pos = np.zeros(dim)
    cg_curve = np.zeros(Max_EFs)
    fit = np.full(SearchAgents_no, np.inf)
    newfit = fit.copy()

    # 主循环
    while EFs < Max_EFs:
        # 单个个体迭代
        for i in range(SearchAgents_no):
            # 边界检查
            newFisher[i] = np.minimum(np.maximum(newFisher[i], lb), ub)
            # 评估目标
            newfit[i] = fobj(newFisher[i])
            # 接受更优解
            if newfit[i] <= fit[i]:
                fit[i] = newfit[i]
                Fisher[i] = newFisher[i].copy()
            # 更新全局最优
            if newfit[i] <= Best_score:
                Best_score = newfit[i]
                Best_pos = Fisher[i].copy()
            # 记录曲线
            cg_curve[EFs] = Best_score
            EFs += 1
            if EFs >= Max_EFs:
                break
        # 如果已经达到评估次数就退出
        if EFs >= Max_EFs:
            break

        # 根据当前评估比例选择搜索阶段
        if EFs < Max_EFs / 2:
            alpha = (1 - 3 * EFs / (2 * Max_EFs)) ** (3 * EFs / (2 * Max_EFs))
            p = np.random.rand()
            pos = np.random.permutation(SearchAgents_no)
            i = 0
            while i < SearchAgents_no and EFs < Max_EFs:
                per = np.random.randint(3, 5)  # 3 或 4
                if p < alpha or i + per > SearchAgents_no:
                    # Independent search
                    # 随机选另一个个体 r != i
                    r = np.random.randint(SearchAgents_no)
                    while r == i:
                        r = np.random.randint(SearchAgents_no)
                    Exp = (fit[pos[i]] - fit[pos[r]]) / (fit.max() - Best_score + 1e-16)
                    rs = np.random.randn(dim)
                    rs = (np.linalg.norm(Fisher[r] - Fisher[pos[i]])
                          * (1 - EFs / Max_EFs)
                          * rs / np.linalg.norm(rs))
                    newFisher[pos[i]] = (Fisher[pos[i]]
                                         + (Fisher[r] - Fisher[pos[i]]) * Exp
                                         + np.sqrt(abs(Exp)) * rs)
                    i += 1
                else:
                    # Group capture
                    group_idx = pos[i:i+per]
                    weights = fit[group_idx] / (fit[group_idx].sum() + 1e-16)
                    aim = np.dot(weights, Fisher[group_idx])
                    rand_mat = np.random.rand(per, dim)
                    newFisher[group_idx] = (
                        Fisher[group_idx]
                        + rand_mat * (aim - Fisher[group_idx])
                        + (1 - 2 * EFs / Max_EFs) * (np.random.rand(per, dim) * 2 - 1)
                    )
                    i += per
        else:
            # Collective capture
            sigma = np.sqrt(2 * (1 - EFs / Max_EFs) / ((1 - EFs / Max_EFs)**2 + 1))
            for i in range(SearchAgents_no):
                W = np.abs(Best_pos - Fisher.mean(axis=0)) * (np.random.randint(1,4)/3) * sigma
                newFisher[i] = Best_pos + np.random.normal(0, W, size=dim)

    return Best_pos, Best_score, cg_curve
