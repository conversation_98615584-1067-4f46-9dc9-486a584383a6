import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401

from utils.functions_details import get_functions_details
from utils.cfoa import cfoa
from utils.plotting import func_plot
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 设置中文字体为黑体
matplotlib.rcParams['axes.unicode_minus'] = False    # 正常显示负号


def main():
    # 参数设置
    SearchAgents = 30           # 群体规模
    Max_iterations = 1000       # 最大迭代次数
    func_name = 'F1'            # 选定优化函数，可替换为 'F1'~'F23'

    # 获取函数详情
    lb, ub, dim, fobj = get_functions_details(func_name)

    # 调用算法
    best_pos, best_score, curve = cfoa(
        SearchAgents, Max_iterations, lb, ub, dim, fobj
    )
    print(f"Best position: {best_pos}\nBest score: {best_score}")

    # 绘图
    fig = plt.figure(figsize=(12, 5), facecolor='white')

    # 子图1：目标函数 3D 曲面
    ax1 = fig.add_subplot(1, 2, 1, projection='3d')
    # func_plot.plots a new figure by default; instead replicate its logic here to use ax1
    # 生成 x,y 网格和 Z 值
    lb_xy, ub_xy, _, fobj_xy = get_functions_details(func_name)
    # 仅二维函数可视化
    x = np.linspace(lb_xy if np.isscalar(lb_xy) else lb_xy[0],
                    ub_xy if np.isscalar(ub_xy) else ub_xy[0], 100)
    X, Y = np.meshgrid(x, x)
    Z = np.zeros_like(X)
    for i in range(X.shape[0]):
        for j in range(X.shape[1]):
            xi, yi = X[i, j], Y[i, j]
            if func_name == 'F15':
                args = [xi, yi, 0, 0]
            elif func_name == 'F19':
                args = [xi, yi, 0]
            elif func_name == 'F20':
                args = [xi, yi, 0, 0, 0, 0]
            elif func_name in {'F21','F22','F23'}:
                args = [xi, yi, 0, 0]
            else:
                args = [xi, yi]
            Z[i, j] = fobj_xy(np.array(args))
    ax1.plot_surface(X, Y, Z, linewidth=0, antialiased=True)
    ax1.contourf(X, Y, Z, zdir='z', offset=Z.min(), alpha=0.7)
    ax1.set_title(func_name)
    ax1.set_xlabel('x')
    ax1.set_ylabel('y')
    ax1.set_zlabel('f(x,y)')

    # 子图2：收敛曲线
    ax2 = fig.add_subplot(1, 2, 2)
    CNT = 20
    idx = np.round(np.linspace(0, Max_iterations - 1, CNT)).astype(int)
    iterations = np.arange(1, Max_iterations + 1)
    if func_name not in {'F16', 'F9', 'F11'}:
        ax2.semilogy(iterations[idx], curve[idx], 'r->', linewidth=1)
    else:
        ax2.plot(iterations[idx], curve[idx], 'r->', linewidth=1)
    ax2.grid(True)
    ax2.set_title('收敛曲线')
    ax2.set_xlabel('迭代次数')
    ax2.set_ylabel('适应度值')
    ax2.legend(['CFOA'])
    
    plt.tight_layout()
    plt.show()


if __name__ == '__main__':
    main()
